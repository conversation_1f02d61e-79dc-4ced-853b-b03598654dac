FROM php:8.3-fpm-alpine

# Install system dependencies, including development packages for gd dependencies
RUN apk update && apk add --no-cache \
    git \
    curl \
    zip \
    unzip \
    libzip-dev \
    icu-dev \
    oniguruma-dev \
    libxml2-dev \
    postgresql-dev \
    libpng-dev \
    libjpeg-turbo-dev \
    && docker-php-ext-install -j$(nproc) \
    pdo pdo_mysql zip intl bcmath xml gd mbstring soap

#install ssh for ssh tunneling
RUN apk add openssh-client


# Install PHP extensions
# RUN docker-php-ext-install pdo pdo_mysql mbstring exif pcntl bcmath gd
#RUNdocker-php-ext-install pdo pdo_mysql

# Download the Digital Ocean CA certificate for MySQL
# RUN curl -o /usr/local/share/ca-certificates/digitalocean-db-ca.crt https://cacerts.digicert.com/DigiCertGlobalRootCA.crt.pem \
#     && chmod 0644 /usr/local/share/ca-certificates/digitalocean-db-ca.crt \
#     && update-ca-certificates
    
COPY /bin/prod/backend/ca-certificate.crt /usr/local/share/ca-certificates/do-ca.crt      
RUN  chmod 0644 /usr/local/share/ca-certificates/do-ca.crt \
     && update-ca-certificates

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Copy existing application directory and ignore var/www/storage/ directory all subfolders files  copy fonly sub directories of /var/www/storage


#COPY --chown=www-data:www-data --chmod=766 --exclude='var/www/storage/*' elysium-backend /var/www
#COPY -r --parents elysium-backend/ /var/www/ && find elysium-backend/storage -type f -exec rm "/var/www/{}" \;
COPY elysium-backend /var/www


# Copy main application files excluding storage
COPY elysium-backend /var/www/elysium-backend

# Remove any files in storage directory
RUN find /var/www/storage -type f -delete

RUN touch /var/www/cron.log
RUN chmod 755 /var/www/cron.log

# Ensure proper permissions
RUN chown -R www-data:www-data /var/www \
    && chmod -R 766 /var/www/storage \
    && chmod -R 766 /var/www/bootstrap/cache

# COPY bin/prod/backend.env /var/www/.env

# Set working directory
WORKDIR /var/www

# COPY /var/www/public/ssh_keys/elysium /var/www/public/ssh_keys/id_rsa
RUN chmod 400 /var/www/public/ssh_keys/id_rsa

# RUN composer update
RUN composer update
RUN composer install --no-dev --optimize-autoloader --no-interaction --no-progress --prefer-dist

# Generate application key (you might want to handle this differently in production)
# RUN php artisan key:generate
# RUN php artisan optimize:clear
# RUN php artisan config:clear
# RUN php artisan optimize


# Set permissions for the container
# RUN chown -R www-data:www-data /var/www
# RUN chmod -R 766 /var/www/storage /var/www/bootstrap/cache

# create cron job to run as www-data user


RUN php artisan key:generate && php artisan optimize:clear && php artisan optimize && php artisan config:clear

# RUN echo "* * * * * cd /var/www && php artisan schedule:run >> /var/www/cron.log 2>&1" > /etc/crontabs/root

# Install cron
RUN apk add --no-cache busybox-suid dcron

# Create directory for cron files
RUN mkdir -p /var/spool/cron/crontabs

# Copy your crontab file
# COPY crontab /var/spool/cron/crontabs/root

RUN touch /var/spool/cron/crontabs/root
# RUN chmod 600 /var/spool/cron/crontabs/www-data
RUN echo "* * * * * cd /var/www && php artisan schedule:run >> /var/www/cron.log 2>&1" > /var/spool/cron/crontabs/root


# Give execution rights on the cron job
RUN chmod 0644 /var/spool/cron/crontabs/root

# Apply cron job
RUN crontab /var/spool/cron/crontabs/root

# Create the log file to be able to run tail
RUN touch /var/log/cron.log

# #enable crontab running on boot

#make sure cron is running
# RUN crontab -u www-data /var/spool/cron/crontabs/www-data

# Expose port 9000
EXPOSE 9000

# crond -f -l 8 -L /var/log/cron.log
# Start PHP-FPM
CMD ["php-fpm"]
