<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\UserController;
use App\Http\Controllers\API\StateController;
use App\Http\Controllers\API\CompanyController;
use App\Http\Controllers\API\CountryController;
use App\Http\Controllers\API\Auth\OtpController;
use App\Http\Controllers\API\DBServerController;
use App\Http\Controllers\API\TimezoneController;
use App\Http\Controllers\API\TableStatsController;
use App\Http\Controllers\API\DatabaseStatController;
use App\Http\Controllers\API\DBServerTypeController;
use App\Http\Controllers\API\RemoteServerController;
use App\Http\Controllers\API\SubscriptionController;
use App\Http\Controllers\API\ResetPasswordController;
use App\Http\Controllers\API\ClientDatabaseController;
use App\Http\Controllers\API\ClientDbServerController;
use App\Http\Controllers\API\Stripe\BillingController;
use App\Http\Controllers\API\ProcessScheduleController;
use App\Http\Controllers\API\SubscriptionPlanController;
use App\Http\Controllers\API\ObjectStorageTypeController;
use App\Http\Controllers\API\Schedule\ScheduleController;
use App\Http\Controllers\API\ClientDbServerStatController;
use App\Http\Controllers\API\ClientObjectStorageContoller;
use App\Http\Controllers\API\RemoteServerFilterController;
use App\Http\Controllers\API\TableProcessStatusController;
use App\Http\Controllers\API\ClientDBServerTableController;
use App\Http\Controllers\API\DatabaseTableOptionController;
use App\Http\Controllers\API\DatabaseServerOptionController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::post('/auth/register', [AuthController::class, 'register']);
Route::post('/auth/login', [AuthController::class, 'login']);
Route::post('/reset_password', [ResetPasswordController::class, 'resetPassword']);
Route::post('/request_otp', [OtpController::class, 'requestOtp']);
Route::post('/verify_otp', [OtpController::class, 'verifyOtp']);
Route::get('/countries', [CountryController::class, 'getAll']);
Route::get('/states', [StateController::class, 'getAll']);
Route::post('/schedule_response', [ProcessScheduleController::class,'populate_stats']);
Route::resource('timezones', TimezoneController::class);

Route::resource('serverOptions', DatabaseServerOptionController::class);
Route::resource('tableOptions', DatabaseTableOptionController::class);
Route::post('/showInvoice', [BillingController::class, 'showInvoice']);
Route::resource('companies', CompanyController::class);
Route::get('/downloadInvoice/{userId}/{invoiceId}', [BillingController::class, 'downloadInvoice']);

Route::post('/getDBResponse', [DBServerController::class, 'getDBResponse']);
Route::post('/test-remote-tunnel-connection', [DBServerController::class, 'testDynamicSshTunnelConnection']);

Route::middleware(['auth:sanctum'])->group(function () {
	Route::get('/getSchedule', [ProcessScheduleController::class, 'getSchedule']);
	Route::resource('subscriptionPlans', SubscriptionPlanController::class);
	Route::resource('users', UserController::class);
	Route::post('subscription', [SubscriptionController::class, 'createSubscription'])->name("subscription.create");
	Route::post('/auth/logout', [AuthController::class, 'logout']);
	Route::post('trialSubscription', [SubscriptionController::class, 'trialSubscription'])->name("trial.subscription");
	Route::post('getSetupIntent', [SubscriptionController::class, 'getSetupIntent'])->name("intent.subscription");
	Route::post('/filterSubscriptionPlans', [SubscriptionPlanController::class, 'filterSubscriptionPlans']);
	Route::post('/updateUserProfilePassword', [UserController::class, 'updateUserProfilePassword']);
	Route::post('/getInvoices', [BillingController::class, 'getInvoices'])->withoutMiddleware("throttle:api");
	Route::post('/getPaymentMethods', [BillingController::class, 'getPaymentMethods'])->withoutMiddleware("throttle:api");
	Route::post('/updatePaymentMethod', [BillingController::class, 'updatePaymentMethod']);
	Route::post('/auth/user', [UserController::class, 'getAuthUser']);
	Route::post('/updateSubscriptionPlan', [SubscriptionController::class, 'updateSubscriptionPlan']);
	Route::post('/cancelSubscriptionPlan', [SubscriptionController::class, 'cancelSubscriptionPlan']);
	Route::post('/updateUserProfile', [UserController::class, 'updateUserProfile']);
	Route::post('/getActiveSubscription', [BillingController::class, 'getActiveSubscription'])->withoutMiddleware("throttle:api");
	Route::post('/getRemoteServerFilters', [RemoteServerFilterController::class, 'getRemoteServerFilters']);
	Route::post('/getAllRemoteServer/{status_id?}', [RemoteServerController::class, 'getAllRemoteServer']);
	Route::post('/createRemoteServer', [RemoteServerController::class, 'createRemoteServer']);
	Route::post('/getRemoteServer/{id}', [RemoteServerController::class, 'getRemoteServer']);
	Route::post('/updateRemoteServer/{id}', [RemoteServerController::class, 'updateRemoteServer']);
	Route::post('/deleteRemoteServer', [RemoteServerController::class, 'deleteRemoteServer']);
	Route::post('/getAllDBServerTypes', [DBServerTypeController::class, 'getAllDBServerTypes']);
	Route::post('/getAllDBServer/{status_id?}/{remote_server_id?}', [DBServerController::class, 'getAllDBServer']);
	Route::post('/createDBServer', [DBServerController::class, 'createDBServer']);
	Route::post('/deleteDBServer', [DBServerController::class, 'deleteDBServer']);
	Route::post('/updateDBServer/{id}', [DBServerController::class, 'updateDBServer']);
	Route::post('/storeDatabaseInfomation', [DBServerController::class, 'storeDatabaseInfomation']);
	Route::post('/getAllDatabasesList', [ClientDatabaseController::class, 'getAllDatabasesList']);
	Route::post('/deleteDBSchema', [ClientDatabaseController::class, 'deleteClientDatabase']);
	Route::post('/getAllDatabasesTablesList', [ClientDBServerTableController::class, 'getAllDatabasesTablesList']);
	Route::post('/getTableProcessStatusess', [TableProcessStatusController::class, 'getTableProcessStatusess']);
	Route::post('/getObjectStorageTypes', [ObjectStorageTypeController::class, 'getObjectStorageTypes']);
	Route::post('/getAllCloudProviderNames', [ObjectStorageTypeController::class, 'getAllCloudProviderNames']);

	Route::post('/getClientObjectStorage', [ClientObjectStorageContoller::class, 'getClientObjectStorage']);
	Route::post('/storeClientObjectStorage', [ClientObjectStorageContoller::class, 'storeClientObjectStorage']);

	Route::post('/dashboardTotalArchiveStats', [ClientDbServerStatController::class, 'dashboardTotalArchiveStats']);
	Route::post('/dashboardServersListing', [ClientDbServerStatController::class, 'dashboardServersListing']);
	Route::post('/dashboardServersBarChart', [ClientDbServerStatController::class, 'dashboardServersBarChart']);
	Route::post('/dashboardActivityLogs', [ClientDbServerStatController::class, 'dashboardActivityLogs']);
	Route::post('/serversTotalArchiveStats', [ClientDbServerStatController::class, 'serversTotalArchiveStats']);
	Route::post('/serverDatabasesBarChart', [ClientDbServerStatController::class, 'serverDatabasesBarChart']);
	Route::post('/serverDatabasesListing', [ClientDbServerStatController::class, 'serverDatabasesListing']);
	Route::post('/databasesTotalArchiveStats', [DatabaseStatController::class, 'databasesTotalArchiveStats']);
	Route::post('/databaseTablesListing', [DatabaseStatController::class, 'databaseTablesListing']);
	Route::post('/databaseTablesBarChart', [DatabaseStatController::class, 'databaseTablesBarChart']);
	Route::post('/tablesTotalArchiveStats', [TableStatsController::class, 'tablesTotalArchiveStats']);
	Route::post('/tableArchivingActivities', [TableStatsController::class, 'tableArchivingActivities']);
	Route::post('/tablesLineChart', [TableStatsController::class, 'tablesLineChart']);

	Route::prefix('schedule')->group(
		function ()
		{
			Route::post('/serversListing', [ScheduleController::class, 'serverListing']);
			Route::post('/databasesListingByServerId', [ScheduleController::class, 'databaseByServerId']);
			Route::post('/tablesListingByDatabaseId', [ScheduleController::class, 'tablesListingByDbId']);
			Route::post('/add', [ScheduleController::class, 'addScheduleListing']);
			Route::post('/listing', [ScheduleController::class, 'scheduleListing']);
			Route::post('/setupArchiving', [ScheduleController::class, 'setScheduleTime']);
			Route::post('/getArchivingById', [ScheduleController::class, 'editScheduleTime']);
			Route::post('/remove', [ScheduleController::class, 'deleteSingleSchedule']);
			Route::post('/removeDatabase', [ScheduleController::class, 'deleteDatabaseSchedule']);
			Route::post('/changeArchivingStatus', [ScheduleController::class, 'changeArchivingState']);
			Route::post('/restore', [ScheduleController::class, 'restoreSchedule']);
			Route::post('/databaseRestore', [ScheduleController::class, 'restoreDatabaseSchedule']);
		}
	);

	Route::post('/testRemoteServerConnection/{id}', [RemoteServerController::class, 'testRemoteServerConnection']);
	Route::post('/runNetworkDiagnostics/{id}', [RemoteServerController::class, 'runNetworkDiagnostics']);
	Route::post('/testDBServerConnection/{id}', [DBServerController::class, 'testDBServerConnection']);
	Route::post('/runDBNetworkDiagnostics/{id}', [DBServerController::class, 'runDBNetworkDiagnostics']);
});
