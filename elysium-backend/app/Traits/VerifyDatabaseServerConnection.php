<?php

namespace App\Traits;

use phpseclib3\Net\SSH2;
use phpseclib3\Net\SFTP;
use App\Models\Port;
use App\Models\RemoteServer;
use App\Models\ClientDbServer;
use Illuminate\Support\Facades\Log;
use phpseclib3\Crypt\PublicKeyLoader;
use Illuminate\Support\Facades\DB;

trait VerifyDatabaseServerConnection
{	/**
	 * Verify remote server credentials
	 * @param RemoteServer $remoteServer The remote server to connect through
	 * @param string $hostname Database server hostname
	 * @param string $username Database username
	 * @param string $password Database password
	 * @param int $port Database port
	 * @param string $uuid Database server UUID
	 * @param array &$logs Optional array to collect connection logs
	 * @return array Status and message
	 */

	 public static function connect($remoteServer, $hostname, $username, $password, $port, $uuid)
    {
		try {
			$startTime = microtime(true);
			$response = [];

			$sshHostName = $remoteServer->hostname;
			$sshUserName = $remoteServer->username;
			$sshPort = $remoteServer->port;
			$remoteServerId = $remoteServer->id;

			// Create an SSH2 instance
			$ssh = new SSH2($sshHostName, $sshPort);

			 // Ensure channel is closed before attempting new connection
			if ($ssh->isConnected()) {
				$ssh->disconnect();
			}

			// Authenticate with the private key
			$path = public_path('ssh_keys/id_rsa');

			$key = PublicKeyLoader::load(file_get_contents($path));


			if (!$ssh->login($remoteServer->username, $key)) {
				Log::info("Login failed for the hostname $sshHostName");
				$response = [ 'status' => false, 'message' => "Failed to connect to remote server using dynamic SSH tunnel."];
				throw new \Exception('Login Failed');

			} else {
				Log::info("Remote Server Connection established for hostname $sshHostName");
				$endTime = microtime(true);
				$executionTime = $endTime - $startTime;
				Log::info("Remote Server SSH login took " . $executionTime . " seconds to execute.");
				$startTime = microtime(true);
				try {
						$localPort = self::getNextAvailablePort();
						Log::info('local Port');
						Log::info($localPort);

						// Save port in the table
						$assignedPort = new Port;
						$assignedPort->port = $localPort;
						$assignedPort->save();

						// Establish SSH tunnel
						$tunnelCommand = "ssh -o StrictHostKeyChecking=no -p {$sshPort} -L {$localPort}:{$hostname}:{$port} -N -f -l {$sshUserName} -i {$path} {$sshHostName}";

						Log::info('Tunnel Command');
						Log::info($tunnelCommand);



						exec($tunnelCommand. ' 2>&1', $output, $execCode);
						Log::info('Tunnel command output');
						Log::info($output);
						Log::info($execCode);

						// return ['status' => false, 'message' => $tunnelCommand. ' 2>&1', $output, $execCode];
						// Connect to the remote database
						DB::connection('remote_mysql')->disconnect();

						config([
							'database.connections.remote_mysql.host' => '127.0.0.1',
							'database.connections.remote_mysql.port' => $localPort,
							'database.connections.remote_mysql.database' => '',
							'database.connections.remote_mysql.username' => $username,
							'database.connections.remote_mysql.password' => $password,
						]);

						// Debugging: Print the configured values
						$config = config('database.connections.remote_mysql');
						Log::info('Config Information');
						Log::info($config);

						DB::connection('remote_mysql')->reconnect();

						// Check if the connection was successful
						$connected = DB::connection('remote_mysql')->getPdo() != null;
						Log::info('Connected');
						Log::info($connected);

						if($connected) {
							$endTime = microtime(true);
							$executionTime = $endTime - $startTime;
							Log::info("SSH tunnel took " . $executionTime . " seconds to execute.");
							$startTime = microtime(true);
							Log::info("Connected to remote MySQL database using dynamic SSH tunnel. for the db host $hostname");

							$appEnv = config('app.env');
							Log::info($appEnv);

							// if($appEnv == 'development') {
							// 	$baseUrl = 'https://api-dev.elysium-io.com/';
							// } else {
							// 	$baseUrl = 'https://elysium.au.ngrok.io/';
							// }
							
							$baseUrl = env('BASE_URL');
							Log::info('Base URL');
							Log::info($baseUrl);

							$WEBHOOK_URL= $baseUrl. 'api/getDBResponse';

							Log::info('WEBHOOK URL');

							Log::info($WEBHOOK_URL);

							//Writing MySQL credentials into a config file on the remote server
							$configContent = <<<EOT
							REMOTE_DB_HOST="$hostname"
							REMOTE_DB_USER="$username"
							REMOTE_DB_PASSWORD="$password"
							REMOTE_DB_PORT="$port"
							DB_SERVER_UUID="$uuid"
							WEBHOOK_URL="$WEBHOOK_URL"
							EOT;

							Log::info('Writing vars in config file');
							// Command to create a directory if it doesn't exist
							$remoteDirectory = '~/remote';
							$createRemoteDirectoryCommand = "mkdir -p $remoteDirectory";
							// Execute the command
							$output_2 = $ssh->exec($createRemoteDirectoryCommand);

							$ssh->exec('echo \'' . $configContent . '\' > ~/remote/mysql_config.txt');

							$endTime = microtime(true);
							$executionTime = $endTime - $startTime;
							$startTime = microtime(true);
							Log::info("Writing config variables took " . $executionTime . " seconds to execute.");

							Log::info('Moving shell script on server');
							Log::info('Shell script start');

							// Copy the script to the remote server

							// Path to the local Bash script
							$localScriptPath = public_path('shell_scripts/db_info.sh');
														

							// Check if the directory was created successfully
							if ($output_2 === false) {
								Log::info("Directory creation failed");
							} else {
								Log::info("Directory created successfully");
							}

							// Path where you want to copy the script on the remote server
							$remoteScriptPath = '~/remote/db_script.sh';

							$scriptContent = file_get_contents($localScriptPath);

							// Escape single quotes in the script content
							$scriptContent = str_replace("'", "'\''", $scriptContent);

							$ssh->exec("echo '{$scriptContent}' > {$remoteScriptPath}");
							$ssh->exec("sudo chmod 766 {$remoteScriptPath}");

							Log::info('Script has been written to db_script.sh on the server.');

							$endTime = microtime(true);
							$executionTime = $endTime - $startTime;
							Log::info("Writing bash script took " . $executionTime . " seconds to execute.");
							$startTime = microtime(true);

							// Execute the script remotely
							$output = $ssh->exec('cd remote && ls');
							Log::info($output);
							$output =$ssh->exec("cd remote && bash $remoteScriptPath");
							Log::info($output);

							$endTime = microtime(true);
							$executionTime = $endTime - $startTime;
							Log::info("Executing bash script took " . $executionTime . " seconds to execute.");
							$startTime = microtime(true);

							// Insall mysql-client and configure --login-path
							$output =$ssh->exec("sudo apt-get update && sudo apt-get install -y mysql-client && sudo apt-get install -y expect");
							Log::info($output);

							$client_db_server = ClientDbServer::where('client_db_server_uuid', $uuid)->first();

							//rplace all spaces with _ in db_server_name and add elysium_ prefix
							// $login_path_name = 'elysium_'.str_replace(' ', '_', strtolower($client_db_server->db_server_name));
							// $login_path_name = 'elysium_'.$client_db_server->db_server_name;
							// Use expect to securely handle password input with special characters
							$expectScript = <<<EOT
							expect -c '
							spawn mysql_config_editor set --login-path=$client_db_server->login_path_name --host=$hostname --user=$username --port=$port --password
							expect "Enter password:"
							send "$password\r"
							expect eof
							'
							EOT;
							$output = $ssh->exec($expectScript);
							Log::info($output);

							//update client_db_server.login_path_name with $login_path_name
							
							// $client_db_server->login_path_name = $login_path_name;
							// $client_db_server->save();


							// create new directory on server ~/agent, copy shell_scripts/db_archive.sh to remote server and create dir data_archive
							$output =$ssh->exec("cd ~ && mkdir -p ~/agent && mkdir -p ~/agent/data_archive");
							Log::info($output);

							//get BASE_URL value from .env and save to $webhook_url variable
							$WEBHOOK_URL = $baseUrl;
							
							//Writing Agent config variables into a config file on the remote server
							$AgentConfigContent = <<<EOT
							ARCHIVE_DIR="/home/<USER>/agent/data_archive"
							ARCHIVE_THREAD_LIMIT=2
							S3_BUCKET="mysql-data-archive-incremental"
							EMAIL_TO="<EMAIL>,<EMAIL>"
							WEBHOOK_URL="$WEBHOOK_URL" 
							S3_TRY_MAX=10
							EOT;

							Log::info('Writing vars in config file');

							$ssh->exec('echo \'' . $AgentConfigContent . '\' > ~/agent/agent_config.txt');

							
							$localScriptPath = public_path('shell_scripts/db_archive.sh');
							$remoteScriptPath = '~/agent/db_archive.sh';
							$scriptContent = file_get_contents($localScriptPath);
							$scriptContent = str_replace("'", "'\''", $scriptContent);
							$ssh->exec("echo '{$scriptContent}' > {$remoteScriptPath}");
							$ssh->exec("sudo chmod 766 {$remoteScriptPath}");
							$output =$ssh->exec("cd agent && ls");
							Log::info($output);

							// Close the SSH tunnel when done
							Log::info('Closing ssh mysql tunnel 111');
							$closeTunnelCommand = "pkill -f '{$tunnelCommand}'";
							exec($closeTunnelCommand);

							$deleteLocalPort = Port::where('port', $localPort)->delete();
							Log::info('Delete Local Port Status');
							Log::info($deleteLocalPort);

							$endTime = microtime(true);
							$executionTime = $endTime - $startTime;
							Log::info("Closing and deleting port took " . $executionTime . " seconds to execute.");

							$response = [ 'status' => true, 'message' => "Connected to remote MySQL database using dynamic SSH tunnel."];
						} else {
							Log::info("Failed to connect to remote MySQL database using dynamic SSH tunnel $hostname");
							$response = [ 'status' => false, 'message' => "Failed to connect to remote MySQL database using dynamic SSH tunnel.1"];
						}

					} catch (\Exception $e) {
						// Debugging: Print the exception message
						Log::info("Exception occur for the hostname $sshHostName and DB host $hostname");
						Log::info($e);

						// Return response message
						$response = [ 'status' => false, 'message' => $e." Failed to connect to remote MySQL database using dynamic SSH tunnel.2"];
						return $response;
					} finally {
						// Close the SSH tunnel when done
						$closeTunnelCommand = "pkill -f '{$tunnelCommand}'";
						exec($closeTunnelCommand);
					}

				$ssh->disconnect();
				return $response;
			}
		} catch (\Throwable $th) {
			Log::error($th->getMessage());
			$ssh->disconnect();
			Log::error("An error occurred during SSH login. Please try again later for the hostname $hostname");
			$response = [ 'status' => false, 'message' => 'Connection failed'];
			return $response;
		}
    }

	public static function connect_test($remoteServer, $hostname, $username, $password, $port, $uuid, &$logs = [])
	{
		try {
			if (is_array($logs)) {
				$logs[] = "Starting database connection test...";
				$logs[] = "Remote server: {$remoteServer->hostname}";
				$logs[] = "Database server: {$hostname}:{$port}";
			}
			
			// Get the path to the private key
			$path = public_path('ssh_keys/id_rsa');
			
			if (!file_exists($path)) {
				if (is_array($logs)) {
					$logs[] = "ERROR: SSH key not found at {$path}";
				}
				return ['status' => false, 'message' => "SSH key not found"];
			}
			
			if (is_array($logs)) {
				$logs[] = "Using SSH key: {$path}";
			}
			
			// Create an SSH2 instance
			$ssh = new SSH2($remoteServer->hostname, $remoteServer->port, 10);
			
			// Authenticate with the private key
			$key = PublicKeyLoader::load(file_get_contents($path));
			
			if (is_array($logs)) {
				$logs[] = "Connecting to remote server {$remoteServer->hostname}:{$remoteServer->port}...";
			}
			
			if (!$ssh->login($remoteServer->username, $key)) {
				if (is_array($logs)) {
					$logs[] = "ERROR: Failed to connect to remote server {$remoteServer->hostname}";
				}
				return ['status' => false, 'message' => "Failed to connect to remote server"];
			}
			
			if (is_array($logs)) {
				$logs[] = "Successfully connected to remote server";
				$logs[] = "Testing connection to database server {$hostname}:{$port}...";
			}
			
			// First, check if the database server is reachable from the remote server
			if (is_array($logs)) {
				$logs[] = "Step 1: Checking if database server is reachable...";
				$logs[] = "Executing command: nc -z -v -w5 {$hostname} {$port}";
			}
			
			$netcatResult = $ssh->exec("nc -z -v -w5 {$hostname} {$port} 2>&1");
			
			if (is_array($logs)) {
				$logs[] = "Result: " . trim($netcatResult);
			}
			
			if (strpos($netcatResult, 'succeeded') === false && strpos($netcatResult, 'open') === false) {
				if (is_array($logs)) {
					$logs[] = "ERROR: Cannot reach database server {$hostname} on port {$port}";
					$logs[] = "Possible issues:";
					$logs[] = "1. Database server is not running";
					$logs[] = "2. Firewall is blocking the connection";
					$logs[] = "3. Incorrect hostname or port";
					
					// Try to ping the database server
					$logs[] = "Attempting to ping database server...";
				}
				
				$pingResult = $ssh->exec("ping -c 3 -W 5 {$hostname} 2>&1");
				
				if (is_array($logs)) {
					$logs[] = "Ping result:";
					foreach (explode("\n", $pingResult) as $line) {
						if (trim($line)) {
							$logs[] = trim($line);
						}
					}
				}
				
				return ['status' => false, 'message' => "Cannot reach database server {$hostname} on port {$port}"];
			}
			
			// Now test the MySQL connection directly from the remote server
			if (is_array($logs)) {
				$logs[] = "Step 2: Testing MySQL connection...";
				$logs[] = "Executing MySQL connection test...";
			}
			
			// Escape special characters in the password
			$escapedPassword = str_replace("'", "'\\''", $password);
			
			$mysqlCommand = "mysql -h {$hostname} -u {$username} -p'{$escapedPassword}' -P {$port} -e 'SELECT 1' 2>&1";
			
			if (is_array($logs)) {
				$logs[] = "Executing command: mysql -h {$hostname} -u {$username} -P {$port} -e 'SELECT 1'";
			}
			
			$mysqlResult = $ssh->exec($mysqlCommand);
			
			if (strpos($mysqlResult, 'ERROR') !== false) {
				if (is_array($logs)) {
					$logs[] = "MySQL connection failed:";
					$logs[] = trim($mysqlResult);
					$logs[] = "Possible issues:";
					$logs[] = "1. Incorrect username or password";
					$logs[] = "2. Database server not allowing connections from this host";
					$logs[] = "3. MySQL service not running";
				}
				
				return ['status' => false, 'message' => "MySQL connection failed: " . trim(preg_replace('/ERROR \d+.*?: /', '', $mysqlResult))];
			}
			
			if (is_array($logs)) {
				$logs[] = "MySQL connection successful!";
				$logs[] = "Step 3: Checking database server version...";
			}
			
			// Get MySQL version
			$versionCommand = "mysql -h {$hostname} -u {$username} -p'{$escapedPassword}' -P {$port} -e 'SELECT VERSION()' 2>&1";
			$versionResult = $ssh->exec($versionCommand);
			
			if (is_array($logs)) {
				$logs[] = "Database server version:";
				$logs[] = trim(preg_replace('/VERSION\(\)/', '', $versionResult));
			}
			
			// List available databases
			if (is_array($logs)) {
				$logs[] = "Step 4: Listing available databases...";
			}
			
			$dbListCommand = "mysql -h {$hostname} -u {$username} -p'{$escapedPassword}' -P {$port} -e 'SHOW DATABASES' 2>&1";
			$dbListResult = $ssh->exec($dbListCommand);
			
			if (is_array($logs)) {
				$logs[] = "Available databases:";
				foreach (explode("\n", $dbListResult) as $line) {
					if (trim($line) && trim($line) != 'Database' && 
						!in_array(trim($line), ['information_schema', 'performance_schema', 'mysql', 'sys'])) {
						$logs[] = trim($line);
					}
				}
			}
			
			// Close the SSH connection
			$ssh->disconnect();
			
			if (is_array($logs)) {
				$logs[] = "Connection test completed successfully";
			}
		} catch (\Throwable $th) {
			Log::error($th->getMessage());
			$ssh->disconnect();
			Log::error("An error occurred during SSH login. Please try again later for the hostname $hostname");
			$response = [ 'status' => false, 'message' => 'Connection failed'];
			return $response;
		}
    }

	/**
	 * Get next available port
	 */
	public static function getNextAvailablePort()
	{
		$port = 13306;

		do {
			$existingPort = Port::where('port', $port)->first();

			if (!$existingPort) {
				// If the port doesn't exist in the database, return it
				return $port;
			}

			// Increment the port by 1 for the next check
			$port++;
		} while (true);
	}

	public static function testConnect()
	{
		$sshHostName = "***********";
		$sshUserName = "ubuntu";
		$sshPort = 2222;
		$hostname = "elysium-test-db.c2tvnpmo3doh.us-east-1.rds.amazonaws.com";
		$username = "svc_elysium";
		$password = "pguD.8uA8!2yaZjbVLA!";

		// Create an SSH2 instance
		$ssh = new SSH2($sshHostName, $sshPort);

		// Authenticate with the private key
		$path = '/Users/<USER>/Downloads/franck-ssh-host-elysium.pem';

		$key = PublicKeyLoader::load(file_get_contents($path));

		if (!$ssh->login($sshUserName, $key)) {
			Log::info("Login failed for the hostname $sshHostName");
			return [ 'status' => false, 'message' => "Failed to connect to remote server using dynamic SSH tunnel."];
		}

		Log::info("Remote Server Connected established for hostname $sshHostName");
		try {
			$localPort = 13306;
			$remotePort = 3306;

			// Establish SSH tunnel
			$tunnelCommand = "ssh -p {$sshPort} -L {$localPort}:{$hostname}:{$remotePort} -N -f -l {$sshUserName} -i {$path} {$sshHostName}";
			Log::info($tunnelCommand);

			$database = 'sakila';

			exec($tunnelCommand);
			// Connect to the remote database
			DB::connection('remote_mysql')->disconnect();
			config([
				'database.connections.remote_mysql.host' => '127.0.0.1',
				'database.connections.remote_mysql.port' => $localPort,
				'database.connections.remote_mysql.database' => $database,
				'database.connections.remote_mysql.username' => $username,
				'database.connections.remote_mysql.password' => $password,
			]);
			// Debugging: Print the configured values
			$config = config('database.connections.remote_mysql');
			Log::info($config);

			DB::connection('remote_mysql')->reconnect();
			Log::info('asdssadasdsa');
			// Check if the connection was successful
			$connected = DB::connection('remote_mysql')->getPdo() != null;
			Log::info('Connected');
			Log::info($connected);

			if($connected) {
				Log::info("Connected to remote MySQL database using dynamic SSH tunnel. for the db host $hostname");

				Log::info('Shell script start');

				// Copy the script to the remote server

				// Path to the local Bash script
				// $localScriptPath = public_path('shell_scripts/db_info.sh');

				// Path where you want to copy the script on the remote server
				$remoteScriptPath = '~/remote/db_script.sh';

				// $scriptContent = file_get_contents($localScriptPath);

				// Escape single quotes in the script content
				// $scriptContent = str_replace("'", "'\''", $scriptContent);

				// $ssh->exec("echo '{$scriptContent}' > {$remoteScriptPath}");

				Log::info('Script has been written to db_script.sh on the server.');

				// Execute the script remotely
				$command = "cd remote && bash {$remoteScriptPath}";
				$output = $ssh->exec($command);
				Log::info($output);
				// Close the SSH tunnel when done
				Log::info('Closing ssh mysql tunnel 111');
				$closeTunnelCommand = "pkill -f '{$tunnelCommand}'";
				exec($closeTunnelCommand);

				$response = [ 'status' => true, 'message' => "Connected to remote MySQL database using dynamic SSH tunnel."];
			} else {
				Log::info("Failed to connect to remote MySQL database using dynamic SSH tunnel $hostname");
				$response = [ 'status' => false, 'message' => "Failed to connect to remote MySQL database using dynamic SSH tunnel."];
			}
		} catch (\Exception $e) {
			Log::info($e);
			// Debugging: Print the exception message
			Log::info($e->getMessage(). " for the hostname $sshHostName and DB host $hostname");
			$response = [ 'status' => false, 'message' => "Failed to connect to remote MySQL database using dynamic SSH tunnel."];
			// Connection failed
			$connected = false;
		} finally {
			// Close the SSH tunnel when done
			Log::info('Closing ssh mysql tunnel');
			$closeTunnelCommand = "pkill -f '{$tunnelCommand}'";
			exec($closeTunnelCommand);
		}


		$ssh->disconnect();
		return $response;
	}
}
