<?php

namespace App\Http\Controllers\API;

use App\Models\ClientDBSchema;
use Illuminate\Support\Facades\Auth;
use App\Models\ClientDBServerTable;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\DBServer;
use App\Models\RemoteServer;
use App\Traits\ApiResponser;
use App\Models\ClientDatabase;
use App\Http\Resources\DBServerResource;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\DBServerRequest;
use App\Http\Requests\UpdateDBServerRequest;
use App\Models\ClientDBServerTableColumn;
use App\Traits\VerifyDatabaseServerConnection;
use App\Mail\DatabaseConnectionVerificationEmail;
use Illuminate\Support\Facades\DB;
use App\Jobs\sendDatabaseConnectionVerificationEmailJob;
use App\Models\TableProcessStatus;

class DBServerController extends Controller
{
	use ApiResponser;
    /**
     * Display a listing of the resource.
     */
    public function getAllDBServer(Request $request)
    {
		try {
			$startTime = microtime(true);

			$statusId = $request->status_id ?? null;
			$company_id = Auth::user()->company_id;

			$dbServers = DBServer::where('is_active', 1);

			if($statusId) {
				$dbServers = $dbServers->where('remote_server_status_id', $statusId);
			}

			$dbServers = $dbServers->where('is_deleted', 0)->where('company_id', $company_id)->latest()->get();

			$endTime = microtime(true);
			$executionTime = $endTime - $startTime;
			Log::info("Get All DB Servers Query took " . $executionTime . " seconds to execute.");

			return $this->success('All database servers retrieved successfully', [
				'dbServers' => DBServerResource::collection($dbServers)
			]);
		} catch (\Throwable $th) {
			Log::error($th);
			return $this->error('Something went wrong!', 400);
		}
    }

    /**
     * Store a newly created resource in storage.
     */
    public function createDBServer(DBServerRequest $request)
    {
		try {
			$startTime = microtime(true);
			$dbServerName = $request->db_server_name;
			$dbServerAliasName = str_replace(' ', '-', $dbServerName);
			$hostname = $request->hostname;
			$username = $request->username;
			$password = $request->password;
			$port = $request->port ?? 22;
			$uuid = $request->client_db_server_uuid;
			$remoteServerId = $request->remote_server_id;
			$remoteServer = RemoteServer::find($remoteServerId);

			if($remoteServer && $remoteServer->remote_server_status_id !== 1) {
				return $this->error('Remote server connection failed!', 400);
			}

			$typeId = $request->client_db_server_type_id;
			$status = $request->has('is_active') ? $request->is_active : 0;

			$defaultStatusId = 3;

			$dbServer = new DBServer;
			$dbServer->db_server_name = $dbServerName;
			$dbServer->db_server_alias_name = $dbServerAliasName;
			$dbServer->hostname = $hostname;
			$dbServer->username = $username;
			$dbServer->password = $password;
			$dbServer->port = $port;
			$dbServer->client_db_server_uuid = $uuid;
			$dbServer->remote_server_id = $remoteServerId;
			$dbServer->client_db_server_type_id = $typeId;
			$dbServer->is_active = $status;
			$dbServer->login_path_name =  'elysium_'.str_replace(' ', '_', strtolower($dbServerName));
			$dbServer->remote_server_status_id = $defaultStatusId;
			$dbServer->company_id = $remoteServer->company_id;
			$dbServer->save();

			$endTime = microtime(true);
			$executionTime = $endTime - $startTime;
			Log::info("Storing db server information took " . $executionTime . " seconds to execute.");

			$lastInsertedId = $dbServer->id;

			if($lastInsertedId) {
				$startTime = microtime(true);
				$databaseServerConnection = VerifyDatabaseServerConnection::connect($remoteServer, $hostname, $username, $password, $port, $uuid);
				Log::info('databaseServerConnection');
				Log::info($databaseServerConnection);

				$endTime = microtime(true);
				$executionTime = $endTime - $startTime;
				Log::info("VerifyDatabaseServerConnection function took " . $executionTime . " seconds to execute.");

				if($databaseServerConnection && isset($databaseServerConnection['status']) && $databaseServerConnection['status'] === true) {
					$statusID = 1;
				} else {
					$statusID = 3;
				}

				$dbServer = DBServer::find($lastInsertedId);

				$dbServer->remote_server_status_id = $statusID;
				$dbServer->save();

				$startTime = microtime(true);
				$this->sendDatabaseConnectionStatusEmail($hostname, $dbServerName, $statusID);

				$endTime = microtime(true);
				$executionTime = $endTime - $startTime;
				Log::info("Sending database connection status email took " . $executionTime . " seconds to execute.");

				return $this->success('Database server successfully added',
					[
						'remoteServer' => new DBServerResource($dbServer)
					]
				);
			} else {
				return $this->error('Something went wrong!', 200);
			}

		} catch (\Throwable $th) {
			Log::info($th);
			return $this->error('Something went wrong!', 400);
		}
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
		try {
			$dbServer = DBServer::find($id);
			if(!$dbServer) {
				return $this->error('No record found', 404);
			}

			return $this->success('Database server information retrieved successfully!',
				[
					'dbServer' => new DBServerResource($dbServer)
				]
			);

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Update the specified resource in storage.
     */
    public function updateDBServer(UpdateDBServerRequest $request, string $id)
    {
		try {
			$startTime = microtime(true);
			$dbServer = DBServer::find($id);

			if(!$dbServer) {
				return $this->error('Invalid ID, record not found!', 400);
			}

			$remoteServerId = $request->remote_server_id;

			$remoteServer = RemoteServer::find($remoteServerId);

			if(!$remoteServer) {
				return $this->error('Invalid Remote Server ID, record not found!', 400);
			}

			$dbServerName = $request->db_server_name;
			$dbServerAliasName = str_replace(' ', '-', $dbServerName);
			$hostname = $request->hostname;
			$username = $request->username;
			$password = $request->password;
			$port = $request->port ?? 22;
			$uuid = $request->client_db_server_uuid;
			$defaultStatusId = 3;
			$typeId = $request->client_db_server_type_id;
			$status = $request->has('is_active') ? $request->is_active : 0;

			$dbServer->company_id = $remoteServer->company_id;
			$dbServer->db_server_name = $dbServerName;
			$dbServer->db_server_alias_name = $dbServerAliasName;
			$dbServer->hostname = $hostname;
			$dbServer->username = $username;
			$dbServer->password = $password;
			$dbServer->port = $port;
			$dbServer->client_db_server_uuid = $uuid;
			$dbServer->remote_server_id = $remoteServerId;
			$dbServer->remote_server_status_id = $defaultStatusId;
			$dbServer->remote_server_status_id = $typeId;
			$dbServer->is_active = $status;
			$dbServer->save();

			$endTime = microtime(true);
			$executionTime = $endTime - $startTime;
			Log::info("Updating db server information took " . $executionTime . " seconds to execute.");

			if($dbServer) {
				$startTime = microtime(true);
				$databaseServerConnection = VerifyDatabaseServerConnection::connect($remoteServer, $hostname, $username, $password, $port, $uuid);
				Log::info('databaseServerConnection');
				Log::info($databaseServerConnection);

				$endTime = microtime(true);
				$executionTime = $endTime - $startTime;
				Log::info("VerifyDatabaseServerConnection function took " . $executionTime . " seconds to execute.");

				if($databaseServerConnection && isset($databaseServerConnection['status']) && $databaseServerConnection['status'] === true) {
					$statusID = 1;
				} else {
					$statusID = 3;
				}

				$dbServer = DBServer::find($id);

				$dbServer->remote_server_status_id = $statusID;
				$dbServer->save();

				$startTime = microtime(true);
				$this->sendDatabaseConnectionStatusEmail($hostname, $dbServerName, $statusID);

				$endTime = microtime(true);
				$executionTime = $endTime - $startTime;
				Log::info("Sending database connection status email took " . $executionTime . " seconds to execute.");

				return $this->success('Database server updated successfully',
					[
						'remoteServer' => new DBServerResource($dbServer)
					]
				);
			} else {
				return $this->error('Something went wrong while updating DB Server!', 400);
			}

		} catch (\Throwable $th) {
			return $this->error('Something went wrong!', 400);
		}
    }

	/**
	 * Send Remote Connection Status Email
	 *
	 */
	public function sendDatabaseConnectionStatusEmail($hostName = '', $serverName = '', $statusId = 3) {
		if($hostName && $serverName) {
			$user = Auth::user();

			if($user)  {
				$firstName = $user->first_name;
				$email = $user->email;
			}


			$data = [
				'firstName' => $firstName,
				'hostName' => $hostName,
				'serverName' => $serverName,
				'statusId' => $statusId,
				'email' => $email

			];

			dispatch(new sendDatabaseConnectionVerificationEmailJob($data));
		}
	}

	public function getDBResponse(Request $request) {

		Log::info('Webhook Triggered');

		$startTime = microtime(true);

		$data = $request->all();

		$endTime = microtime(true);
		$executionTime = $endTime - $startTime;
		Log::info("Fetching all data from webhook took " . $executionTime . " seconds to execute.");
		// Log::info($data);
		$uuid = $request->uuid;
		Log::info($uuid);

		//Log::info($request->headers->all());
		Log::info($request->getClientIp());

		$startTime = microtime(true);

		$uuid = $request->header('authorization');
		$ipAddress = $request->header('x-forwarded-for');

		if($uuid) {
			// Split the string by 'Bearer ' and get the second part
			$parts = explode('Bearer ', $uuid);

			if (count($parts) === 2) {
				$uuid = $parts[1];

				// Check if uuid and ip address is valid
				$dbServer = DBServer::where('client_db_server_uuid', $uuid)->first();

				$endTime = microtime(true);
				$executionTime = $endTime - $startTime;
				Log::info("Fetching db server information took " . $executionTime . " seconds to execute.");

				Log::info('DB Server Information');
				Log::info($dbServer);

				if($dbServer) {
					$startTime = microtime(true);
					$dbServerId = $dbServer->id;
					$response = $this->storeDatabaseInfomation($data, $dbServerId);

					$endTime = microtime(true);
					$executionTime = $endTime - $startTime;
					Log::info("Storing databases, tables and columns total took " . $executionTime . " seconds to execute.");

					Log::info($response);
					return $response;
				} else {
					Log::info("Invalid UUID");
				}

			} else {
				Log::info("Bearer token not found or invalid format provided");
			}

		} else {
			Log::info('Authentication failed, no uuid found');
		}

		$endTime = microtime(true);
		$executionTime = $endTime - $startTime;
		Log::info("Webhook function total took " . $executionTime . " seconds to execute.");
	}

	/**
     * Remove the specified resource from storage.
     */
    public function deleteDBServer(Request $request)
    {
		try {
			$ids = $request->ids;

			if($ids) {

				foreach($ids as $id) {
					$dbServer = DBServer::find($id);
					if(!$dbServer) {
						return $this->error('Invalid DB Server ID', 404);
					}

					$dbServer->is_deleted = 1;
					$dbServer->save();

					$dbServer->delete();
				}

				return $this->success('Deleted successfully!', []);

			} else {
				return $this->error('No DB Server ID provided', 404);
			}

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

	public function storeDatabaseInfomation($data = [], $dbServerId = null)
	{
		Log::info('Create or update database related information script started');

		// First, ensure table process status records exist
		$this->ensureTableProcessStatusExists();

		if($data && count($data) > 0) {

			foreach ($data as $key => $row) {

				$dbName =  $row['database'];


				// Update DB server information

				$dbServer = DBServer::find($dbServerId);
				$dbServer->total_current_db_storage_setup_size = $row['total_rows'];
				$dbServer->timezone = NULL;
				$dbServer->save();

				if(isset($dbServerId) && isset($dbName)) {



					// Store or update client database information

					$clientDatabase = [
						'client_db_server_id' => $dbServerId,
						'db_name' => $dbName,
						'total_current_db_size' =>  $row['total_db'],
						'total_current_db_data_size' => $row['data'],
						'total_current_db_index_size' => $row['index'],
						'total_table' => isset($row['table_data']) ? count($row['table_data']) : 0
					];

					$matchClientDatabase = [
						'client_db_server_id' => $dbServerId,
						'db_name' => $dbName
					];

					$clientDatabase = ClientDatabase::updateOrCreate($matchClientDatabase, $clientDatabase);

					// Store or update client db schema information

					$matchClientDBSchema = [
						'client_database_id' => $clientDatabase->id,
						'schema_name' => $dbName,
					];

					$dbSchema = [
						'schema_name' => $dbName
					];

					$clientDBSchema = ClientDBSchema::updateOrCreate($matchClientDBSchema, $dbSchema);

					$lastInsertId = $clientDBSchema->id;

					// Create or update tables information
					if(isset($lastInsertId) && $lastInsertId && isset($row['table_data']) && count($row['table_data']) > 0)
					{
						$tables = $row['table_data'];

						foreach ($tables as $index => $table) {

							$tableName = $table['table_name'];

							$tableMatches = [
								'table_name' => $tableName,
								'client_db_schema_id' => $lastInsertId
							];

							$tableData = [
								'client_database_id' => $clientDatabase->id,
								'client_db_server_id' => $dbServerId,
								'client_db_schema_id' => $lastInsertId,
								'client_db_server_table_uuid' => Str::uuid()->toString(),
								'table_schema_name' => '',
								'table_database_name' => $dbName,
								'table_name' => $tableName,
								'table_type' => $table['table_type'],
								'engine' => $table['engine'],
								'version' =>  $table['version'] != "NULL" ? $table['version'] : 0,
								'row_format' => $table['row_format'],
								'total_current_table_rows' => $table['total_current_table_rows'] != "NULL" ? $table['total_current_table_rows'] : 0,
								'total_current_data_length' => $table['total_current_data_length'] != "NULL" ? $table['total_current_data_length'] : 0,
								'avg_row_length' => $table['avg_row_length'] != "NULL" ? $table['avg_row_length'] : 0,
								'max_data_length' => $table['max_data_length'] != "NULL" ? $table['max_data_length'] : 0,
								'data_free' => $table['data_free'] != "NULL" ? $table['data_free'] : 0,
								'auto_increment' => $table['auto_increment'] != "NULL" ? $table['auto_increment'] : 0,
								'create_time' => $table['create_time'] != "NULL" ? $table['create_time'] : NULL,
								'update_time' => $table['update_time'] != "NULL" ? $table['update_time'] : NULL,
								'check_time' => $table['check_time'] != "NULL" ? $table['check_time'] : NULL,
								'table_collation' => $table['table_collation'],
								'checksum' => $table['checksum'] != "NULL" ? $table['checksum'] : NULL,
								'create_options' => $table['create_options'],
								'table_comment' => $table['table_comment'],
								'timezone' => NULL,
								'table_process_status_id' => isset($table['table_process_status_id']) ? $table['table_process_status_id'] : 1,
								'has_reference_integrity' => isset($table['has_reference_integrity']) ? $table['has_reference_integrity'] : 0,
								'columns' => isset($table['columns']) && count($table['columns']) > 0 ? $table['columns'] : []
							];

							$clientDBServerTable = ClientDBServerTable::updateOrCreate($tableMatches, $tableData);
							Log::info("Table created or updated successfully $tableName total rows = ". $row['total_rows'] );
							$lastTableInsertId = $clientDBServerTable->id;

							$allColumnsData = [];

							if(isset($table['columns']) && count($table['columns']) > 0 && $lastTableInsertId) {

								$columns = $table['columns'];

								foreach ($columns as $key => $column) {

									$columnName = $column['column_name'];

									$columnMatches = [
										'column_name' => $columnName,
										'client_db_server_table_id' => $lastTableInsertId
									];

									$columnData = [
										'client_db_server_table_id' => $lastTableInsertId,
										'table_schema_name' => '',
										'table_database_name' => $dbName,
										'table_name' => $tableName,
										'column_name' => $column['column_name'],
										'ordinal_position' => $column['ordinal_position'],
										'is_nullable' => $column['is_nullable'],
										'data_type' => $column['data_type'],
										'character_max_length' => $column['character_max_length'] != "NULL" ? $column['character_max_length'] : 0,
										'column_type' => $column['column_type'],
										'column_key' => $column['column_key'],
										'column_comment' => $column['column_comment']
									];

									ClientDBServerTableColumn::updateOrCreate($columnMatches, $columnData);
								}
							}
						}

						#compare existing tables with incoming $tables array and if an exisitng table is missing update table column is_dropped=1
						$existingTables = ClientDBServerTable::where('client_db_schema_id', $lastInsertId)->where('is_dropped', 0)->get();
						$existingTableNames = $existingTables->pluck('table_name')->toArray();
						$incomingTableNames = array_column($tables, 'table_name');

						$tablesToDrop = array_diff($existingTableNames, $incomingTableNames);

						if (!empty($tablesToDrop)) {
							foreach ($tablesToDrop as $tableToDrop) {
								$table = ClientDBServerTable::where('client_db_schema_id', $lastInsertId)->where('table_name', $tableToDrop)->first();
								if ($table) {
									$table->is_dropped = 1;
									$table->save();
								}
							}
						}
					}
				} else {
					Log::info('DB Server or database name not found');
				}
			}
		}

		Log::info('Create or update database related information script ended');
		return "Data inserted or updated successfully!";
	}

	/**
	 * Ensure table process status records exist
	 */
	private function ensureTableProcessStatusExists()
	{
		$statuses = [
			['id' => 1, 'table_action_name' => 'No Action'],
			['id' => 2, 'table_action_name' => 'Export'],
			['id' => 3, 'table_action_name' => 'Archive'],
			['id' => 4, 'table_action_name' => 'Analyze']
		];

		foreach ($statuses as $status) {
			TableProcessStatus::updateOrCreate(
				['id' => $status['id']],
				['table_action_name' => $status['table_action_name']]
			);
		}
		
		Log::info('Table process statuses verified/created');
	}

	public function testDynamicSshTunnelConnection(Request $request)
    {
        try {

            $host = $request->input('host');
            $database = $request->input('database');
            $username = $request->input('username');
            $password = $request->input('password');
            $sshHost = $request->input('ssh_host');
            $sshUser = $request->input('ssh_user');
            // $sshKeyPath = $request->input('ssh_key_path');
            $sshKeyPath = '/Users/<USER>/Downloads/franck-ssh-host-elysium.pem';
            $sshPort = $request->input('ssh_port', 22);
            $localPort = 13306;
            $remotePort = 3306;

            // Establish SSH tunnel
            $tunnelCommand = "ssh -p {$sshPort} -L {$localPort}:{$host}:{$remotePort} -N -f -l {$sshUser} -i {$sshKeyPath} {$sshHost}";

            exec($tunnelCommand, $output, $execCode);
            dump($execCode);

            // Connect to the remote database
            DB::connection('remote_mysql')->disconnect();
            config([
                'database.connections.remote_mysql.host' => '127.0.0.1',
                'database.connections.remote_mysql.port' => $localPort,
                'database.connections.remote_mysql.database' => $database,
                'database.connections.remote_mysql.username' => $username,
                'database.connections.remote_mysql.password' => $password,
            ]);

            // Debugging: Print the configured values
            $config = config('database.connections.remote_mysql');
            dump($config);

            DB::connection('remote_mysql')->reconnect();

            // Check if the connection was successful
            $connected = DB::connection('remote_mysql')->getPdo() != null;
        } catch (\Exception $e) {
            // Debugging: Print the exception message
            dump($e->getMessage());

            // Connection failed
            $connected = false;
        } finally {
            // Close the SSH tunnel when done
            $closeTunnelCommand = "pkill -f '{$tunnelCommand}'";
            exec($closeTunnelCommand);
        }

        return $connected
            ? response()->json(['status' => 'Connected to remote MySQL database using dynamic SSH tunnel.'])
            : response()->json(['status' => 'Failed to connect to remote MySQL database using dynamic SSH tunnel.']);
    }

    /**
     * Test connection to a database server
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function testDBServerConnection($id)
    {
        try {
            $dbServer = DBServer::findOrFail($id);
            $remoteServer = RemoteServer::find($dbServer->remote_server_id);
            
            if (!$remoteServer) {
                return $this->error('Remote server not found for this database server', 404);
            }
            
            // Start logging connection details
            $connectionLogs = [];
            $connectionLogs[] = "Starting connection test to database server {$dbServer->hostname}...";
            $connectionLogs[] = "Using remote server: {$remoteServer->hostname}";
            $connectionLogs[] = "Using database port: {$dbServer->port}";
            $connectionLogs[] = "Using username: {$dbServer->username}";
            
            // Test the connection
            $databaseServerConnection = VerifyDatabaseServerConnection::connect(
                $remoteServer, 
                $dbServer->hostname, 
                $dbServer->username, 
                $dbServer->password, 
                $dbServer->port, 
                $dbServer->client_db_server_uuid,
                $connectionLogs
            );
            
            if ($databaseServerConnection && isset($databaseServerConnection['status']) && $databaseServerConnection['status'] === true) {
                // Update the status in the database
                $dbServer->remote_server_status_id = 1; // Connected
                $dbServer->save();
                
                return $this->success('Successfully connected to database server', [
                    'logs' => $connectionLogs
                ]);
            } else {
                // Update the status in the database
                $dbServer->remote_server_status_id = 3; // Failed
                $dbServer->save();
                
                $errorMessage = isset($databaseServerConnection['message']) 
                    ? $databaseServerConnection['message'] 
                    : 'Failed to connect to database server';
                    
                return $this->error($errorMessage, 400, [
                    'logs' => $connectionLogs
                ]);
            }
        } catch (\Throwable $th) {
            Log::error($th);
            return $this->error('Failed to test database server connection: ' . $th->getMessage(), 400);
        }
    }

    /**
     * Run network diagnostics for a database server
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function runDBNetworkDiagnostics($id)
    {
        try {
            $dbServer = DBServer::findOrFail($id);
            $remoteServer = RemoteServer::find($dbServer->remote_server_id);
            
            if (!$remoteServer) {
                return $this->error('Remote server not found for this database server', 404);
            }
            
            $diagnosticLogs = [];
            $diagnosticLogs[] = "Running network diagnostics for database server {$dbServer->hostname}:{$dbServer->port}...";
            $diagnosticLogs[] = "Via remote server: {$remoteServer->hostname}:{$remoteServer->port}";
            
            // First check if we can connect to the remote server
            $diagnosticLogs[] = "Step 1: Testing connection to remote server {$remoteServer->hostname}...";
            
            // Create an SSH2 instance
            $ssh = new \phpseclib3\Net\SSH2($remoteServer->hostname, $remoteServer->port, 10);
            
            // Get the path to the private key
            $keyPath = public_path('ssh_keys/id_rsa');
            if (!file_exists($keyPath)) {
                $diagnosticLogs[] = "ERROR: SSH key not found at {$keyPath}";
                return $this->error('SSH key not found', 400, [
                    'logs' => $diagnosticLogs
                ]);
            }
            
            // Load the private key
            $key = \phpseclib3\Crypt\PublicKeyLoader::load(file_get_contents($keyPath));
            
            // Try to login
            if (!$ssh->login($remoteServer->username, $key)) {
                $diagnosticLogs[] = "ERROR: Failed to connect to remote server {$remoteServer->hostname}";
                $diagnosticLogs[] = "Cannot proceed with database connection test without remote server access";
                return $this->error('Failed to connect to remote server', 400, [
                    'logs' => $diagnosticLogs
                ]);
            }
            
            $diagnosticLogs[] = "Successfully connected to remote server {$remoteServer->hostname}";
            
            // Now test connection to the database server from the remote server
            $diagnosticLogs[] = "Step 2: Testing connection to database server {$dbServer->hostname} from remote server...";
            
            // Test MySQL connectivity using the remote server
            $command = "nc -z -v -w5 {$dbServer->hostname} {$dbServer->port} 2>&1";
            $diagnosticLogs[] = "Executing command: {$command}";
            
            $result = $ssh->exec($command);
            $diagnosticLogs[] = "Result: " . trim($result);
            
            if (strpos($result, 'succeeded') !== false || strpos($result, 'open') !== false) {
                $diagnosticLogs[] = "TCP connection to database server successful";
                
                // Try a MySQL connection test
                $diagnosticLogs[] = "Step 3: Testing MySQL connection...";
                $mysqlCommand = "mysql -h {$dbServer->hostname} -u {$dbServer->username} -p'{$dbServer->password}' -P {$dbServer->port} -e 'SELECT 1' 2>&1";
                $diagnosticLogs[] = "Executing MySQL connection test...";
                
                $mysqlResult = $ssh->exec($mysqlCommand);
                
                if (strpos($mysqlResult, 'ERROR') !== false) {
                    $diagnosticLogs[] = "MySQL connection failed:";
                    $diagnosticLogs[] = trim($mysqlResult);
                    $diagnosticLogs[] = "Possible issues:";
                    $diagnosticLogs[] = "1. Incorrect username or password";
                    $diagnosticLogs[] = "2. Database server not allowing connections from this host";
                    $diagnosticLogs[] = "3. MySQL service not running";
                } else {
                    $diagnosticLogs[] = "MySQL connection successful!";
                }
            } else {
                $diagnosticLogs[] = "TCP connection to database server failed";
                $diagnosticLogs[] = "Possible issues:";
                $diagnosticLogs[] = "1. Database server is not running";
                $diagnosticLogs[] = "2. Firewall is blocking the connection";
                $diagnosticLogs[] = "3. Incorrect hostname or port";
                
                // Try to ping the database server
                $pingCommand = "ping -c 3 -W 5 {$dbServer->hostname} 2>&1";
                $diagnosticLogs[] = "Attempting to ping database server: {$pingCommand}";
                
                $pingResult = $ssh->exec($pingCommand);
                $diagnosticLogs[] = "Ping result:";
                foreach (explode("\n", $pingResult) as $line) {
                    if (trim($line)) {
                        $diagnosticLogs[] = trim($line);
                    }
                }
            }
            
            return $this->success("Network diagnostics completed", [
                'logs' => $diagnosticLogs
            ]);
            
        } catch (\Throwable $th) {
            Log::error($th);
            return $this->error('Failed to run network diagnostics: ' . $th->getMessage(), 400);
        }
    }
}
