#!/bin/bash

# Database Metrics Collection Scripts for Archiving Workflow
# Usage: Configure DB credentials and run each script at appropriate workflow steps

# =============================================================================
# CONFIGURATION - Update these variables
# =============================================================================
DB_HOST="localhost"
DB_USER="your_username"
DB_PASS="your_password"
DB_NAME="sakila"
DB_PATH="elysium_test-db"
MYSQL_CMD="mysql --login-path=$DB_PATH "
OUTPUT_DIR="./db-archiving"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')

# Create output directory if it doesn't exist
mkdir -p $OUTPUT_DIR

# =============================================================================
# STEP 1: INITIAL DATABASE SCHEMA COLLECTION
# =============================================================================

step1_initial_schema_collection() {
    echo "=== Step 1: Initial Database Schema Collection - $TIMESTAMP ==="
    
    # 1.1 Get overall database statistics
    echo "Collecting database overview..."
    $MYSQL_CMD -e "
    SELECT 
        '$DB_NAME' as database_name,
        ROUND(SUM(data_length + index_length) / 1024 / 1024 / 1024, 4) AS total_size_gb,
        ROUND(SUM(data_length) / 1024 / 1024 / 1024, 4) AS data_size_gb,
        ROUND(SUM(index_length) / 1024 / 1024 / 1024, 4) AS index_size_gb,
        COUNT(*) as total_tables,
        NOW() as collection_timestamp
    FROM information_schema.tables 
    WHERE table_schema = '$DB_NAME';" > $OUTPUT_DIR/db_overview_$TIMESTAMP.txt
    
    # 1.2 Get detailed table metrics
    echo "Collecting detailed table metrics..."
    $MYSQL_CMD -e "
    SELECT 
        table_name,
        table_rows,
        ROUND((data_length + index_length) / 1024 / 1024, 2) AS total_size_mb,
        ROUND(data_length / 1024 / 1024, 2) AS data_size_mb,
        ROUND(index_length / 1024 / 1024, 2) AS index_size_mb,
        avg_row_length,
        engine,
        table_collation,
        create_time,
        update_time,
        table_comment,
        CASE 
            WHEN table_rows > 0 THEN ROUND((data_length + index_length) / table_rows, 2)
            ELSE 0 
        END as bytes_per_row
    FROM information_schema.tables 
    WHERE table_schema = '$DB_NAME' 
    ORDER BY (data_length + index_length) DESC;" > $OUTPUT_DIR/table_metrics_$TIMESTAMP.txt
    
    # 1.3 Get column information for archiving analysis
    echo "Collecting column metadata..."
    $MYSQL_CMD -e "
    SELECT 
        t.table_name,
        t.column_name,
        t.data_type,
        t.is_nullable,
        t.column_default,
        t.character_maximum_length,
        t.numeric_precision,
        t.numeric_scale,
        t.column_type,
        t.column_key,
        t.extra,
        CASE 
            WHEN t.data_type IN ('date', 'datetime', 'timestamp') THEN 'DATE_COLUMN'
            WHEN t.column_name LIKE '%date%' OR t.column_name LIKE '%time%' THEN 'POTENTIAL_DATE'
            ELSE 'REGULAR'
        END as column_category
    FROM information_schema.columns t
    WHERE t.table_schema = '$DB_NAME'
    ORDER BY t.table_name, t.ordinal_position;" > $OUTPUT_DIR/column_metadata_$TIMESTAMP.txt
    
    # 1.4 Identify tables with date columns for archiving eligibility
    echo "Identifying archiving-eligible tables..."
    $MYSQL_CMD -e "
    SELECT 
        table_name,
        GROUP_CONCAT(DISTINCT column_name ORDER BY column_name) as date_columns,
        COUNT(*) as date_column_count,
        'ELIGIBLE' as archive_status
    FROM information_schema.columns 
    WHERE table_schema = '$DB_NAME' 
        AND (data_type IN ('date', 'datetime', 'timestamp') 
             OR column_name REGEXP '(created_at|updated_at|date|time)')
    GROUP BY table_name
    ORDER BY date_column_count DESC;" > $OUTPUT_DIR/archive_eligible_tables_$TIMESTAMP.txt
    
    # 1.5 Get index information
    echo "Collecting index information..."
    $MYSQL_CMD -e "
    SELECT 
        table_name,
        index_name,
        GROUP_CONCAT(column_name ORDER BY seq_in_index) as indexed_columns,
        index_type,
        COUNT(*) as column_count,
        SUM(cardinality) as total_cardinality
    FROM information_schema.statistics 
    WHERE table_schema = '$DB_NAME'
    GROUP BY table_name, index_name, index_type
    ORDER BY table_name, index_name;" > $OUTPUT_DIR/index_info_$TIMESTAMP.txt
    
    echo "Step 1 completed. Files saved in $OUTPUT_DIR/"
}

# =============================================================================
# STEP 2: SCHEDULED SCHEMA COMPARISON
# =============================================================================

step2_scheduled_comparison() {
    echo "=== Step 2: Scheduled Schema Comparison - $TIMESTAMP ==="
    
    PREV_TIMESTAMP=$1
    if [ -z "$PREV_TIMESTAMP" ]; then
        echo "Error: Previous timestamp required for comparison"
        echo "Usage: step2_scheduled_comparison YYYYMMDD_HHMMSS"
        return 1
    fi
    
    # 2.1 Get current metrics
    echo "Collecting current database metrics..."
    $MYSQL_CMD -e "
    SELECT 
        table_name,
        table_rows as current_rows,
        ROUND((data_length + index_length) / 1024 / 1024, 2) AS current_size_mb,
        avg_row_length as current_avg_row_length,
        NOW() as measurement_time
    FROM information_schema.tables 
    WHERE table_schema = '$DB_NAME' 
    ORDER BY table_name;" > $OUTPUT_DIR/current_metrics_$TIMESTAMP.txt
    
    # 2.2 Calculate growth metrics (requires previous data)
    echo "Calculating growth metrics..."
    $MYSQL_CMD -e "
    SELECT 
        t.table_name,
        t.table_rows,
        ROUND((t.data_length + t.index_length) / 1024 / 1024, 2) AS current_size_mb,
        ROUND(
            (t.table_rows * t.avg_row_length * 24 * 60) / (1024 * 1024), 2
        ) as estimated_daily_growth_mb,
        CASE 
            WHEN t.table_rows > 1000000 THEN 'HIGH_VOLUME'
            WHEN t.table_rows > 100000 THEN 'MEDIUM_VOLUME'
            ELSE 'LOW_VOLUME'
        END as volume_category,
        CASE 
            WHEN (t.data_length + t.index_length) > 1073741824 THEN 'LARGE'
            WHEN (t.data_length + t.index_length) > 104857600 THEN 'MEDIUM'
            ELSE 'SMALL'
        END as size_category
    FROM information_schema.tables t
    WHERE t.table_schema = '$DB_NAME'
    ORDER BY (t.data_length + t.index_length) DESC;" > $OUTPUT_DIR/growth_analysis_$TIMESTAMP.txt
    
    # 2.3 Identify tables needing immediate archiving
    echo "Identifying tables requiring immediate archiving..."
    $MYSQL_CMD -e "
    SELECT 
        t.table_name,
        t.table_rows,
        ROUND((t.data_length + t.index_length) / 1024 / 1024, 2) AS size_mb,
        'URGENT' as priority,
        'Size > 500MB or Rows > 1M' as reason
    FROM information_schema.tables t
    WHERE t.table_schema = '$DB_NAME' 
        AND ((t.data_length + t.index_length) > 524288000 OR t.table_rows > 1000000)
    
    UNION ALL
    
    SELECT 
        t.table_name,
        t.table_rows,
        ROUND((t.data_length + t.index_length) / 1024 / 1024, 2) AS size_mb,
        'HIGH' as priority,
        'Size > 100MB or Rows > 100K' as reason
    FROM information_schema.tables t
    WHERE t.table_schema = '$DB_NAME' 
        AND ((t.data_length + t.index_length) > 104857600 OR t.table_rows > 100000)
        AND NOT ((t.data_length + t.index_length) > 524288000 OR t.table_rows > 1000000)
    
    ORDER BY size_mb DESC;" > $OUTPUT_DIR/archiving_priorities_$TIMESTAMP.txt
    
    # 2.4 Generate comparison report if previous data exists
    if [ -f "$OUTPUT_DIR/table_metrics_$PREV_TIMESTAMP.txt" ]; then
        echo "Generating comparison report..."
        echo "=== Database Growth Report ===" > $OUTPUT_DIR/growth_report_$TIMESTAMP.txt
        echo "Comparison Period: $PREV_TIMESTAMP to $TIMESTAMP" >> $OUTPUT_DIR/growth_report_$TIMESTAMP.txt
        echo "Generated: $(date)" >> $OUTPUT_DIR/growth_report_$TIMESTAMP.txt
        echo "" >> $OUTPUT_DIR/growth_report_$TIMESTAMP.txt
        
        # Note: Actual comparison would require more complex logic
        # This is a template for the comparison structure
        echo "Tables with significant growth (>10% size increase):" >> $OUTPUT_DIR/growth_report_$TIMESTAMP.txt
        echo "Tables with significant row increase (>1000 new rows):" >> $OUTPUT_DIR/growth_report_$TIMESTAMP.txt
        echo "New archiving candidates:" >> $OUTPUT_DIR/growth_report_$TIMESTAMP.txt
    fi
    
    echo "Step 2 completed. Comparison files saved in $OUTPUT_DIR/"
}

# =============================================================================
# STEP 3: ARCHIVAL PROCESS TRACKING
# =============================================================================

step3_archival_tracking() {
    echo "=== Step 3: Archival Process Tracking - $TIMESTAMP ==="
    
    TABLE_NAME=$1
    ARCHIVE_DATE_START=$2
    ARCHIVE_DATE_END=$3
    DATE_COLUMN=${4:-"created_at"}
    
    if [ -z "$TABLE_NAME" ] || [ -z "$ARCHIVE_DATE_START" ] || [ -z "$ARCHIVE_DATE_END" ]; then
        echo "Error: Missing required parameters"
        echo "Usage: step3_archival_tracking TABLE_NAME START_DATE END_DATE [DATE_COLUMN]"
        echo "Example: step3_archival_tracking logs '2024-01-01' '2024-01-31' created_at"
        return 1
    fi
    
    # 3.1 Get BEFORE archival metrics
    echo "Collecting BEFORE archival metrics for table: $TABLE_NAME"
    $MYSQL_CMD -e "
    SELECT 
        '$TABLE_NAME' as table_name,
        table_rows as rows_before,
        ROUND((data_length + index_length) / 1024 / 1024, 2) AS total_size_mb_before,
        ROUND(data_length / 1024 / 1024, 2) AS data_size_mb_before,
        ROUND(index_length / 1024 / 1024, 2) AS index_size_mb_before,
        avg_row_length as avg_row_length_before,
        NOW() as measured_before_timestamp
    FROM information_schema.tables 
    WHERE table_schema = '$DB_NAME' AND table_name = '$TABLE_NAME';" > $OUTPUT_DIR/before_archival_${TABLE_NAME}_$TIMESTAMP.txt
    
    # 3.2 Count rows to be archived
    echo "Counting rows to be archived..."
    $MYSQL_CMD -e "
    SELECT 
        '$TABLE_NAME' as table_name,
        COUNT(*) as rows_to_archive,
        MIN($DATE_COLUMN) as oldest_record,
        MAX($DATE_COLUMN) as newest_record,
        '$ARCHIVE_DATE_START' as archive_start_date,
        '$ARCHIVE_DATE_END' as archive_end_date,
        ROUND(COUNT(*) * 
            (SELECT avg_row_length FROM information_schema.tables 
             WHERE table_schema = '$DB_NAME' AND table_name = '$TABLE_NAME') 
            / 1024 / 1024, 2) as estimated_archive_size_mb
    FROM $DB_NAME.$TABLE_NAME 
    WHERE $DATE_COLUMN >= '$ARCHIVE_DATE_START' 
        AND $DATE_COLUMN <= '$ARCHIVE_DATE_END';" > $OUTPUT_DIR/archive_scope_${TABLE_NAME}_$TIMESTAMP.txt
    
    # 3.3 Get sample of data to be archived (for verification)
    echo "Getting sample of archival data..."
    $MYSQL_CMD -e "
    SELECT *
    FROM $DB_NAME.$TABLE_NAME 
    WHERE $DATE_COLUMN >= '$ARCHIVE_DATE_START' 
        AND $DATE_COLUMN <= '$ARCHIVE_DATE_END'
    ORDER BY $DATE_COLUMN
    LIMIT 5;" > $OUTPUT_DIR/archive_sample_${TABLE_NAME}_$TIMESTAMP.txt
    
    echo "BEFORE metrics collected. Proceed with archival process..."
    echo "After archival is complete, run: step3_post_archival_metrics $TABLE_NAME"
}

step3_post_archival_metrics() {
    echo "=== Step 3: POST-Archival Metrics - $TIMESTAMP ==="
    
    TABLE_NAME=$1
    ARCHIVED_ROWS=$2
    ARCHIVE_FILE_SIZE_MB=$3
    
    if [ -z "$TABLE_NAME" ]; then
        echo "Error: Table name required"
        echo "Usage: step3_post_archival_metrics TABLE_NAME [ARCHIVED_ROWS] [ARCHIVE_FILE_SIZE_MB]"
        return 1
    fi
    
    # 3.4 Get AFTER archival metrics
    echo "Collecting AFTER archival metrics for table: $TABLE_NAME"
    $MYSQL_CMD -e "
    SELECT 
        '$TABLE_NAME' as table_name,
        table_rows as rows_after,
        ROUND((data_length + index_length) / 1024 / 1024, 2) AS total_size_mb_after,
        ROUND(data_length / 1024 / 1024, 2) AS data_size_mb_after,
        ROUND(index_length / 1024 / 1024, 2) AS index_size_mb_after,
        avg_row_length as avg_row_length_after,
        NOW() as measured_after_timestamp
    FROM information_schema.tables 
    WHERE table_schema = '$DB_NAME' AND table_name = '$TABLE_NAME';" > $OUTPUT_DIR/after_archival_${TABLE_NAME}_$TIMESTAMP.txt
    
    # 3.5 Calculate archival impact
    echo "Calculating archival impact..."
    $MYSQL_CMD -e "
    SELECT 
        '$TABLE_NAME' as table_name,
        '$ARCHIVED_ROWS' as rows_archived,
        '$ARCHIVE_FILE_SIZE_MB' as archive_file_size_mb,
        NOW() as calculation_timestamp,
        ROUND(
            (SELECT ROUND((data_length + index_length) / 1024 / 1024, 2) 
             FROM information_schema.tables 
             WHERE table_schema = '$DB_NAME' AND table_name = '$TABLE_NAME') 
            * 0.10, 2) as monthly_db_cost_remaining_usd,
        ROUND('$ARCHIVE_FILE_SIZE_MB' * 0.023, 2) as monthly_s3_cost_usd,
        ROUND(
            ('$ARCHIVE_FILE_SIZE_MB' * 0.10) - ('$ARCHIVE_FILE_SIZE_MB' * 0.023), 2
        ) as estimated_monthly_savings_usd;" > $OUTPUT_DIR/archival_impact_${TABLE_NAME}_$TIMESTAMP.txt
    
    # 3.6 Get updated database overview
    echo "Getting updated database overview..."
    $MYSQL_CMD -e "
    SELECT 
        '$DB_NAME' as database_name,
        ROUND(SUM(data_length + index_length) / 1024 / 1024 / 1024, 4) AS total_size_gb_after,
        COUNT(*) as total_tables,
        NOW() as post_archival_timestamp,
        'POST_ARCHIVAL_$TABLE_NAME' as operation_context
    FROM information_schema.tables 
    WHERE table_schema = '$DB_NAME';" > $OUTPUT_DIR/db_overview_post_archival_${TABLE_NAME}_$TIMESTAMP.txt
    
    # 3.7 Generate archival summary report
    echo "Generating archival summary..."
    echo "=== Archival Summary Report ===" > $OUTPUT_DIR/archival_summary_${TABLE_NAME}_$TIMESTAMP.txt
    echo "Table: $TABLE_NAME" >> $OUTPUT_DIR/archival_summary_${TABLE_NAME}_$TIMESTAMP.txt
    echo "Timestamp: $TIMESTAMP" >> $OUTPUT_DIR/archival_summary_${TABLE_NAME}_$TIMESTAMP.txt
    echo "Rows Archived: $ARCHIVED_ROWS" >> $OUTPUT_DIR/archival_summary_${TABLE_NAME}_$TIMESTAMP.txt
    echo "Archive File Size: ${ARCHIVE_FILE_SIZE_MB}MB" >> $OUTPUT_DIR/archival_summary_${TABLE_NAME}_$TIMESTAMP.txt
    echo "" >> $OUTPUT_DIR/archival_summary_${TABLE_NAME}_$TIMESTAMP.txt
    echo "Before/After Comparison:" >> $OUTPUT_DIR/archival_summary_${TABLE_NAME}_$TIMESTAMP.txt
    echo "Check files: before_archival_${TABLE_NAME}_$TIMESTAMP.txt and after_archival_${TABLE_NAME}_$TIMESTAMP.txt" >> $OUTPUT_DIR/archival_summary_${TABLE_NAME}_$TIMESTAMP.txt
    
    echo "Step 3 POST-archival metrics completed."
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Function to run all steps in sequence
run_full_workflow() {
    echo "Starting full database archiving workflow..."
    step1_initial_schema_collection
    echo "Waiting 5 seconds before step 2..."
    sleep 5
    step2_scheduled_comparison $(date '+%Y%m%d_%H%M%S' -d '1 hour ago')
    echo "Full workflow completed. Review files in $OUTPUT_DIR/"
}

# Function to monitor database growth in real-time
monitor_db_growth() {
    INTERVAL=${1:-300}  # Default 5 minutes
    echo "Monitoring database growth every $INTERVAL seconds. Press Ctrl+C to stop."
    
    while true; do
        echo "=== $(date) ==="
        $MYSQL_CMD -e "
        SELECT 
            table_name,
            table_rows,
            ROUND((data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = '$DB_NAME' 
            AND table_rows > 1000
        ORDER BY (data_length + index_length) DESC 
        LIMIT 10;"
        
        sleep $INTERVAL
    done
}

# Function to get quick database summary
quick_summary() {
    echo "=== Quick Database Summary ==="
    $MYSQL_CMD -e "
    SELECT 
        'Database Size' as metric,
        CONCAT(ROUND(SUM(data_length + index_length) / 1024 / 1024 / 1024, 2), ' GB') as value
    FROM information_schema.tables WHERE table_schema = '$DB_NAME'
    
    UNION ALL
    
    SELECT 
        'Total Tables' as metric,
        COUNT(*) as value
    FROM information_schema.tables WHERE table_schema = '$DB_NAME'
    
    UNION ALL
    
    SELECT 
        'Largest Table' as metric,
        CONCAT(table_name, ' (', ROUND((data_length + index_length) / 1024 / 1024, 2), ' MB)')
    FROM information_schema.tables 
    WHERE table_schema = '$DB_NAME'
    ORDER BY (data_length + index_length) DESC 
    LIMIT 1;"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

case "$1" in
    "step1"|"initial")
        step1_initial_schema_collection
        ;;
    "step2"|"compare")
        step2_scheduled_comparison $2
        ;;
    "step3-pre"|"pre-archive")
        step3_archival_tracking $2 $3 $4 $5
        ;;
    "step3-post"|"post-archive")
        step3_post_archival_metrics $2 $3 $4
        ;;
    "full"|"all")
        run_full_workflow
        ;;
    "monitor")
        monitor_db_growth $2
        ;;
    "summary")
        quick_summary
        ;;
    *)
        echo "Database Archiving Metrics Collection Script"
        echo ""
        echo "Usage: $0 [COMMAND] [PARAMETERS]"
        echo ""
        echo "Commands:"
        echo "  step1, initial                    - Initial schema collection"
        echo "  step2, compare [PREV_TIMESTAMP]   - Scheduled comparison"
        echo "  step3-pre, pre-archive [TABLE] [START_DATE] [END_DATE] [DATE_COLUMN]"
        echo "  step3-post, post-archive [TABLE] [ARCHIVED_ROWS] [FILE_SIZE_MB]"
        echo "  full, all                         - Run complete workflow"
        echo "  monitor [INTERVAL_SECONDS]        - Monitor DB growth in real-time"
        echo "  summary                           - Quick database summary"
        echo ""
        echo "Examples:"
        echo "  $0 step1"
        echo "  $0 step2 20241201_143022"
        echo "  $0 step3-pre logs '2024-01-01' '2024-01-31' created_at"
        echo "  $0 step3-post logs 50000 125.5"
        echo "  $0 monitor 300"
        ;;
esac