#!/bin/bash

# Database connection command
DB_CONN="mysql --login-path=sakila_remote sakila -A"

# Log file for performance tracking
LOG_FILE="insert_performance.log"
> $LOG_FILE  # Clear existing log file or create a new one if it doesn't exist

# Function to generate and insert data into the customer table
generate_customer_data() {
    echo "Inserting data into the customer table..."
    start_time=$(date +%s)
    
    for i in {1..10000}; do
        store_id=$((RANDOM % 2 + 1))  # Assuming there are two stores
        activebool=$((RANDOM % 2))
        create_date=$(date -d "$((RANDOM % 10 + 2010))-01-01 +$RANDOM days" +"%Y-%m-%d")
        last_update=$(date +"%Y-%m-%d %H:%M:%S")
        
        $DB_CONN -e "INSERT INTO customer (store_id, first_name, last_name, email, address_id, active, create_date, last_update) VALUES ($store_id, 'FirstName$i', 'LastName$i', 'email$<EMAIL>', $((RANDOM % 100 + 1)), 1, '$create_date', '$last_update');"
    done

    end_time=$(date +%s)
    echo "Time taken to insert data into customer table: $((end_time - start_time)) seconds" | tee -a $LOG_FILE
}

# Function to generate and insert data into the rental table
generate_rental_data() {
    echo "Inserting data into the rental table..."
    start_time=$(date +%s)
    
    for i in {1..10000}; do
        rental_date=$(date -d "$((RANDOM % 10 + 2010))-01-01 +$RANDOM days" +"%Y-%m-%d %H:%M:%S")
        return_date=$(date -d "$rental_date +$((RANDOM % 14)) days" +"%Y-%m-%d %H:%M:%S")
        last_update=$(date +"%Y-%m-%d %H:%M:%S")

        $DB_CONN -e "set foreign_key_checks=0;INSERT INTO rental (rental_date, inventory_id, customer_id, return_date, staff_id, last_update) VALUES ('$rental_date', $((RANDOM % 500 + 1)), $((RANDOM % 10000 + 1)), '$return_date', $((RANDOM % 2 + 1)), '$last_update');"
    done

    end_time=$(date +%s)
    echo "Time taken to insert data into rental table: $((end_time - start_time)) seconds" | tee -a $LOG_FILE
}

# Function to generate and insert data into the payment table
generate_payment_data() {
    echo "Inserting data into the payment table..."
    start_time=$(date +%s)

    for i in {1..10000}; do
        payment_date=$(date -d "$((RANDOM % 10 + 2010))-01-01 +$RANDOM days" +"%Y-%m-%d %H:%M:%S")
        amount=$(printf "%.2f" "$(echo "$RANDOM/100" | bc -l)")

        $DB_CONN -e "set foreign_key_checks=0; INSERT INTO payment (customer_id, staff_id, rental_id, amount, payment_date) VALUES ($((RANDOM % 10000 + 1)), $((RANDOM % 2 + 1)), $((RANDOM % 10000 + 1)), $amount, '$payment_date');"
    done

    end_time=$(date +%s)
    echo "Time taken to insert data into payment table: $((end_time - start_time)) seconds" | tee -a $LOG_FILE
}

# Function to generate and insert data into the film table
generate_film_data() {
    echo "Inserting data into the film table..."
    start_time=$(date +%s)

    # Arrays for random data generation
    ratings=("G" "PG" "PG-13" "R" "NC-17")
    special_features_options=("Trailers" "Commentaries" "Deleted Scenes" "Behind the Scenes")

    # Sample film titles and descriptions for more realistic data
    film_titles=("Action Hero" "Mystery Night" "Comedy Gold" "Drama Queen" "Sci-Fi Adventure" "Horror Story" "Romance Tale" "Thriller Chase" "Fantasy World" "Documentary Life")

    for i in {1..1000}; do
        # Generate random film data
        title_index=$((RANDOM % ${#film_titles[@]}))
        title="${film_titles[$title_index]} $i"

        # Generate description (50-200 characters)
        description="A captivating story about ${film_titles[$title_index]} that will keep you entertained for hours. Film number $i in our collection."

        # Release year between 1950 and 2024
        release_year=$((RANDOM % 75 + 1950))

        # Language ID (1-6 for standard Sakila languages)
        language_id=$((RANDOM % 6 + 1))

        # Original language ID (nullable, 30% chance of being different from language_id)
        if [ $((RANDOM % 10)) -lt 3 ]; then
            original_language_id=$((RANDOM % 6 + 1))
        else
            original_language_id="NULL"
        fi

        # Rental duration (1-10 days, default 3)
        rental_duration=$((RANDOM % 10 + 1))

        # Rental rate (0.99 to 9.99)
        rental_rate_cents=$((RANDOM % 900 + 99))
        rental_rate=$(awk "BEGIN {printf \"%.2f\", $rental_rate_cents/100}")

        # Length in minutes (60-240 minutes, nullable)
        if [ $((RANDOM % 10)) -lt 9 ]; then
            length=$((RANDOM % 181 + 60))
        else
            length="NULL"
        fi

        # Replacement cost (9.99 to 29.99)
        replacement_cost_cents=$((RANDOM % 2000 + 999))
        replacement_cost=$(awk "BEGIN {printf \"%.2f\", $replacement_cost_cents/100}")

        # Rating
        rating_index=$((RANDOM % ${#ratings[@]}))
        rating="${ratings[$rating_index]}"

        # Special features (can be combination of features or NULL)
        if [ $((RANDOM % 10)) -lt 8 ]; then
            # Generate 1-3 special features
            num_features=$((RANDOM % 3 + 1))
            special_features=""
            used_features=()

            for ((j=0; j<num_features; j++)); do
                feature_index=$((RANDOM % ${#special_features_options[@]}))
                feature="${special_features_options[$feature_index]}"

                # Check if feature already used
                if [[ ! " ${used_features[@]} " =~ " ${feature} " ]]; then
                    if [ -z "$special_features" ]; then
                        special_features="$feature"
                    else
                        special_features="$special_features,$feature"
                    fi
                    used_features+=("$feature")
                fi
            done
            special_features="'$special_features'"
        else
            special_features="NULL"
        fi

        # Insert the film record (excluding film_id as it's AUTO_INCREMENT and last_update has default)
        $DB_CONN -e "INSERT INTO film (title, description, release_year, language_id, original_language_id, rental_duration, rental_rate, length, replacement_cost, rating, special_features) VALUES ('$title', '$description', $release_year, $language_id, $original_language_id, $rental_duration, $rental_rate, $length, $replacement_cost, '$rating', $special_features);"
    done

    end_time=$(date +%s)
    echo "Time taken to insert data into film table: $((end_time - start_time)) seconds" | tee -a $LOG_FILE
}

# Main function to run all data generation functions
main() {
    generate_customer_data
    generate_rental_data
    generate_payment_data
    generate_film_data
}

# Check if script is called with specific function argument
if [ "$1" = "film" ]; then
    generate_film_data
elif [ "$1" = "customer" ]; then
    generate_customer_data
elif [ "$1" = "rental" ]; then
    generate_rental_data
elif [ "$1" = "payment" ]; then
    generate_payment_data
else
    main
fi

