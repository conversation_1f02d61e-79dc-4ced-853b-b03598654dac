#!/bin/bash

# Database connection command
DB_CONN="mysql --login-path=sakila_stg sakila -A"

# Log file for performance tracking
LOG_FILE="insert_performance.log"
> $LOG_FILE  # Clear existing log file or create a new one if it doesn't exist

# Function to generate and insert data into the customer table
generate_customer_data() {
    echo "Inserting data into the customer table..."
    start_time=$(date +%s)
    
    for i in {1..10000}; do
        store_id=$((RANDOM % 2 + 1))  # Assuming there are two stores
        activebool=$((RANDOM % 2))
        create_date=$(date -d "$((RANDOM % 10 + 2010))-01-01 +$RANDOM days" +"%Y-%m-%d")
        last_update=$(date +"%Y-%m-%d %H:%M:%S")
        
        $DB_CONN -e "INSERT INTO customer (store_id, first_name, last_name, email, address_id, active, create_date, last_update) VALUES ($store_id, 'FirstName$i', 'LastName$i', 'email$<EMAIL>', $((RANDOM % 100 + 1)), 1, '$create_date', '$last_update');"
    done

    end_time=$(date +%s)
    echo "Time taken to insert data into customer table: $((end_time - start_time)) seconds" | tee -a $LOG_FILE
}

# Function to generate and insert data into the rental table
generate_rental_data() {
    echo "Inserting data into the rental table..."
    start_time=$(date +%s)
    
    for i in {1..10000}; do
        rental_date=$(date -d "$((RANDOM % 10 + 2010))-01-01 +$RANDOM days" +"%Y-%m-%d %H:%M:%S")
        return_date=$(date -d "$rental_date +$((RANDOM % 14)) days" +"%Y-%m-%d %H:%M:%S")
        last_update=$(date +"%Y-%m-%d %H:%M:%S")

        $DB_CONN -e "set foreign_key_checks=0;INSERT INTO rental (rental_date, inventory_id, customer_id, return_date, staff_id, last_update) VALUES ('$rental_date', $((RANDOM % 500 + 1)), $((RANDOM % 10000 + 1)), '$return_date', $((RANDOM % 2 + 1)), '$last_update');"
    done

    end_time=$(date +%s)
    echo "Time taken to insert data into rental table: $((end_time - start_time)) seconds" | tee -a $LOG_FILE
}

# Function to generate and insert data into the payment table
generate_payment_data() {
    echo "Inserting data into the payment table..."
    start_time=$(date +%s)
    
    for i in {1..10000}; do
        payment_date=$(date -d "$((RANDOM % 10 + 2010))-01-01 +$RANDOM days" +"%Y-%m-%d %H:%M:%S")
        amount=$(printf "%.2f" "$(echo "$RANDOM/100" | bc -l)")

        $DB_CONN -e "set foreign_key_checks=0; INSERT INTO payment (customer_id, staff_id, rental_id, amount, payment_date) VALUES ($((RANDOM % 10000 + 1)), $((RANDOM % 2 + 1)), $((RANDOM % 10000 + 1)), $amount, '$payment_date');"
    done

    end_time=$(date +%s)
    echo "Time taken to insert data into payment table: $((end_time - start_time)) seconds" | tee -a $LOG_FILE
}

# Main function to run all data generation functions
main() {
    generate_customer_data
    generate_rental_data
    generate_payment_data
}

main

